import bcrypt from 'bcryptjs';
import { getConnection, sql } from './database';

export interface User {
  id: number;
  username: string;
  password: string;
  created_at: Date;
}

export async function hashPassword(password: string): Promise<string> {
  const saltRounds = 12;
  return await bcrypt.hash(password, saltRounds);
}

export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return await bcrypt.compare(password, hashedPassword);
}

export async function createUser(username: string, password: string): Promise<boolean> {
  try {
    const pool = await getConnection();
    const hashedPassword = await hashPassword(password);
    
    const result = await pool.request()
      .input('username', sql.VarChar, username)
      .input('password', sql.VarChar, hashedPassword)
      .query(`
        INSERT INTO users (username, password, created_at)
        VALUES (@username, @password, GETDATE())
      `);
    
    return result.rowsAffected[0] > 0;
  } catch (error) {
    console.error('Error creating user:', error);
    return false;
  }
}

export async function authenticateUser(username: string, password: string): Promise<User | null> {
  try {
    const pool = await getConnection();
    
    const result = await pool.request()
      .input('username', sql.VarChar, username)
      .query(`
        SELECT id, username, password, created_at
        FROM users
        WHERE username = @username
      `);
    
    if (result.recordset.length === 0) {
      return null;
    }
    
    const user = result.recordset[0] as User;
    const isValidPassword = await verifyPassword(password, user.password);
    
    if (!isValidPassword) {
      return null;
    }
    
    return user;
  } catch (error) {
    console.error('Error authenticating user:', error);
    return null;
  }
}

export async function initializeDatabase(): Promise<void> {
  try {
    const pool = await getConnection();
    
    // Create users table if it doesn't exist
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='users' AND xtype='U')
      CREATE TABLE users (
        id INT IDENTITY(1,1) PRIMARY KEY,
        username NVARCHAR(50) UNIQUE NOT NULL,
        password NVARCHAR(255) NOT NULL,
        created_at DATETIME NOT NULL DEFAULT GETDATE()
      )
    `);
    
    // Check if default user exists, if not create it
    const result = await pool.request()
      .input('username', sql.VarChar, 'SPB')
      .query('SELECT COUNT(*) as count FROM users WHERE username = @username');
    
    if (result.recordset[0].count === 0) {
      await createUser('SPB', 'Spb@123456');
      console.log('Default user SPB created successfully');
    }
    
  } catch (error) {
    console.error('Error initializing database:', error);
  }
}
