import { NextResponse } from 'next/server';
import { initializeMockDatabase, getMockUsers, hashPasswordSHA256 } from '@/lib/auth-mock';

export async function POST() {
  try {
    initializeMockDatabase();
    
    const users = getMockUsers();
    const testPassword = 'Spb@123456';
    const hashedPassword = hashPasswordSHA256(testPassword);
    
    return NextResponse.json({
      success: true,
      message: 'Mock database initialized successfully',
      users: users,
      testInfo: {
        originalPassword: testPassword,
        hashedPassword: hashedPassword,
        hashLength: hashedPassword.length
      }
    });
  } catch (error) {
    console.error('Mock database initialization error:', error);
    return NextResponse.json(
      { error: 'Failed to initialize mock database' },
      { status: 500 }
    );
  }
}
