# Document System

ระบบจัดการเอกสารที่พัฒนาด้วย Next.js และ SQL Server

## คุณสมบัติ

- ระบบ Login/Authentication ที่ปลอดภัย
- การเข้ารหัสรหัสผ่านด้วย SHA-256 (HASHBYTES)
- เชื่อมต่อฐานข้อมูล SQL Server
- ระบบ Mock Database สำหรับการทดสอบ
- UI ที่ใช้งานง่ายด้วย Tailwind CSS
- Fallback system (Mock → Database)

## การติดตั้งและใช้งาน

### 1. ติดตั้ง Dependencies

```bash
npm install
```

### 2. เริ่มต้นฐานข้อมูล

#### สำหรับ SQL Server Database:
```bash
curl -X POST http://localhost:3000/api/init
```

#### สำหรับ Mock Database (ทดสอบ):
```bash
curl -X POST http://localhost:3000/api/init-mock
```

หรือเข้าไปที่ URL ดังกล่าวในเบราว์เซอร์

### 3. เริ่มต้นเซิร์ฟเวอร์

```bash
npm run dev
```

เปิดเบราว์เซอร์ไปที่ [http://localhost:3000](http://localhost:3000)

## ข้อมูลการเชื่อมต่อฐานข้อมูล

- **Server**: SPD2
- **Database**: Docsystem
- **User**: Singkhorn
- **Password**: Singh@03032531
- **Encryption**: เปิดใช้งาน

## ข้อมูลผู้ใช้เริ่มต้น

- **Username**: SPB
- **Password**: Spb@123456

รหัสผ่านจะถูกเข้ารหัสด้วย bcrypt ก่อนเก็บในฐานข้อมูล

## โครงสร้างไฟล์

```
src/
├── app/
│   ├── api/
│   │   ├── auth/login/route.ts    # API สำหรับ login
│   │   └── init/route.ts          # API สำหรับเริ่มต้นฐานข้อมูล
│   ├── dashboard/page.tsx         # หน้า dashboard หลัง login
│   ├── login/page.tsx             # หน้า login
│   └── page.tsx                   # หน้าแรก (redirect)
├── lib/
│   ├── auth.ts                    # ฟังก์ชัน authentication
│   └── database.ts                # การเชื่อมต่อฐานข้อมูล
└── middleware.ts                  # Middleware สำหรับป้องกันเส้นทาง
```

## การใช้งาน

1. เข้าไปที่ `http://localhost:3000`
2. ระบบจะ redirect ไปหน้า login
3. ใส่ username และ password
4. หลังจาก login สำเร็จจะไปหน้า dashboard
5. สามารถ logout ได้จากปุ่มในหน้า dashboard

## API Endpoints

### Database APIs
- `POST /api/auth/login` - เข้าสู่ระบบ (SQL Server)
- `POST /api/init` - เริ่มต้นฐานข้อมูลและสร้างผู้ใช้เริ่มต้น

### Mock APIs (สำหรับทดสอบ)
- `POST /api/auth/login-mock` - เข้าสู่ระบบ (Mock Database)
- `POST /api/init-mock` - เริ่มต้น Mock Database
- `POST /api/test-hash-mock` - ทดสอบการ hash รหัสผ่าน

### Debug APIs
- `GET /api/test-connection` - ทดสอบการเชื่อมต่อฐานข้อมูล
- `GET /api/debug-user` - ดูข้อมูลผู้ใช้ในฐานข้อมูล

## ความปลอดภัย

- รหัสผ่านถูกเข้ารหัสด้วย SHA-256 (HASHBYTES)
- การเชื่อมต่อฐานข้อมูลใช้ encryption
- Middleware ป้องกันการเข้าถึงหน้าที่ต้อง authentication
- Fallback system เพื่อความเสถียร

## การแก้ปัญหา

### หากไม่สามารถเชื่อมต่อ SQL Server ได้:
1. ตรวจสอบข้อมูล server, username, password
2. ตรวจสอบว่า SQL Server เปิดใช้งาน TCP/IP
3. ตรวจสอบ firewall และ port 1433
4. ระบบจะใช้ Mock Database อัตโนมัติหากเชื่อมต่อไม่ได้

### การทดสอบ:
```bash
# ทดสอบการเชื่อมต่อ
curl http://localhost:3000/api/test-connection

# ทดสอบ Mock system
curl -X POST http://localhost:3000/api/init-mock
```
