# Document System

ระบบจัดการเอกสารที่พัฒนาด้วย Next.js และ SQL Server

## คุณสมบัติ

- ระบบ Login/Authentication ที่ปลอดภัย
- การเข้ารหัสรหัสผ่านด้วย bcrypt
- เชื่อมต่อฐานข้อมูล SQL Server
- UI ที่ใช้งานง่ายด้วย Tailwind CSS

## การติดตั้งและใช้งาน

### 1. ติดตั้ง Dependencies

```bash
npm install
```

### 2. เริ่มต้นฐานข้อมูล

ก่อนใช้งานครั้งแรก ให้เรียก API เพื่อสร้างตารางและผู้ใช้เริ่มต้น:

```bash
curl -X POST http://localhost:3000/api/init
```

หรือเข้าไปที่ `http://localhost:3000/api/init` ในเบราว์เซอร์

### 3. เริ่มต้นเซิร์ฟเวอร์

```bash
npm run dev
```

เปิดเบราว์เซอร์ไปที่ [http://localhost:3000](http://localhost:3000)

## ข้อมูลการเชื่อมต่อฐานข้อมูล

- **Server**: SPD2
- **Database**: Docsystem
- **User**: Singkhorn
- **Password**: Singh@03032531
- **Encryption**: เปิดใช้งาน

## ข้อมูลผู้ใช้เริ่มต้น

- **Username**: SPB
- **Password**: Spb@123456

รหัสผ่านจะถูกเข้ารหัสด้วย bcrypt ก่อนเก็บในฐานข้อมูล

## โครงสร้างไฟล์

```
src/
├── app/
│   ├── api/
│   │   ├── auth/login/route.ts    # API สำหรับ login
│   │   └── init/route.ts          # API สำหรับเริ่มต้นฐานข้อมูล
│   ├── dashboard/page.tsx         # หน้า dashboard หลัง login
│   ├── login/page.tsx             # หน้า login
│   └── page.tsx                   # หน้าแรก (redirect)
├── lib/
│   ├── auth.ts                    # ฟังก์ชัน authentication
│   └── database.ts                # การเชื่อมต่อฐานข้อมูล
└── middleware.ts                  # Middleware สำหรับป้องกันเส้นทาง
```

## การใช้งาน

1. เข้าไปที่ `http://localhost:3000`
2. ระบบจะ redirect ไปหน้า login
3. ใส่ username และ password
4. หลังจาก login สำเร็จจะไปหน้า dashboard
5. สามารถ logout ได้จากปุ่มในหน้า dashboard

## API Endpoints

- `POST /api/auth/login` - เข้าสู่ระบบ
- `POST /api/init` - เริ่มต้นฐานข้อมูลและสร้างผู้ใช้เริ่มต้น

## ความปลอดภัย

- รหัสผ่านถูกเข้ารหัสด้วย bcrypt (salt rounds: 12)
- การเชื่อมต่อฐานข้อมูลใช้ encryption
- Middleware ป้องกันการเข้าถึงหน้าที่ต้อง authentication
