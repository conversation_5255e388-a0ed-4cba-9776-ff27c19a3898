import sql from 'mssql';

const config: sql.config = {
  user: '<PERSON><PERSON><PERSON>',
  password: '<PERSON>@03032531',
  server: 'SPD2',
  database: 'Docsystem',
  port: 1433, // Default SQL Server port
  options: {
    encrypt: true, // Keep encryption enabled
    trustServerCertificate: true,
    enableArithAbort: true,
    instanceName: '', // Add if using named instance
  },
  pool: {
    max: 10,
    min: 0,
    idleTimeoutMillis: 30000
  },
  connectionTimeout: 30000,
  requestTimeout: 30000,
};

let pool: sql.ConnectionPool | null = null;

export async function getConnection(): Promise<sql.ConnectionPool> {
  if (!pool) {
    pool = new sql.ConnectionPool(config);
    await pool.connect();
  }
  return pool;
}

export async function closeConnection(): Promise<void> {
  if (pool) {
    await pool.close();
    pool = null;
  }
}

// Function to initialize Users table with correct structure
export async function initializeUsersTable(): Promise<void> {
  try {
    const pool = await getConnection();

    // Create Users table if it doesn't exist with the specified structure
    // password field is VARCHAR(64) to store SHA-256 hash (64 hex characters)
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Users' AND xtype='U')
      CREATE TABLE Users (
        id INT PRIMARY KEY IDENTITY(1,1),
        username NVARCHAR(50) NOT NULL UNIQUE,
        password VARBINARY(64) NOT NULL,
        created_at DATETIME DEFAULT GETDATE()
      )
    `);

    console.log('Users table initialized successfully');
  } catch (error) {
    console.error('Error initializing Users table:', error);
    throw error;
  }
}

export { sql };
