import sql from 'mssql';

const config: sql.config = {
  user: '<PERSON><PERSON><PERSON>',
  password: '<PERSON>@03032531',
  server: 'SPD2',
  database: 'Docsystem',
  options: {
    encrypt: true, // Use this if you're on Windows Azure
    trustServerCertificate: true, // Use this if you're using self-signed certificates
  },
  pool: {
    max: 10,
    min: 0,
    idleTimeoutMillis: 30000
  }
};

let pool: sql.ConnectionPool | null = null;

export async function getConnection(): Promise<sql.ConnectionPool> {
  if (!pool) {
    pool = new sql.ConnectionPool(config);
    await pool.connect();
  }
  return pool;
}

export async function closeConnection(): Promise<void> {
  if (pool) {
    await pool.close();
    pool = null;
  }
}

// Function to initialize Users table with correct structure
export async function initializeUsersTable(): Promise<void> {
  try {
    const pool = await getConnection();

    // Create Users table if it doesn't exist with the specified structure
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Users' AND xtype='U')
      CREATE TABLE Users (
        id INT PRIMARY KEY IDENTITY(1,1),
        username NVARCHAR(50) NOT NULL UNIQUE,
        password NVARCHAR(255) NOT NULL,
        created_at DATETIME DEFAULT GETDATE()
      )
    `);

    console.log('Users table initialized successfully');
  } catch (error) {
    console.error('Error initializing Users table:', error);
    throw error;
  }
}

export { sql };
