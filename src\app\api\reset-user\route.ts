import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/lib/database';
import { createUser } from '@/lib/auth';

export async function POST() {
  try {
    const pool = await getConnection();
    
    // Delete existing SPB user
    await pool.request()
      .query('DELETE FROM Users WHERE username = \'SPB\'');
    
    // Create new user with SHA-256 hash
    const success = await createUser('SPB', 'Spb@123456');
    
    if (success) {
      // Get the created user to verify
      const result = await pool.request()
        .query('SELECT id, username, password, created_at FROM Users WHERE username = \'SPB\'');
      
      const user = result.recordset[0];
      
      return NextResponse.json({
        success: true,
        message: 'User SPB reset successfully',
        user: {
          id: user.id,
          username: user.username,
          passwordHash: user.password,
          passwordLength: user.password.length,
          created_at: user.created_at
        }
      });
    } else {
      return NextResponse.json(
        { error: 'Failed to create user' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Reset user error:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}
