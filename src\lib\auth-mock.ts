import crypto from 'crypto';

export interface User {
  id: number;
  username: string;
  password: string;
  created_at: Date;
}

// Mock database - in-memory storage
let mockUsers: User[] = [];

export function hashPasswordSHA256(password: string): string {
  return crypto.createHash('sha256').update(password).digest('hex');
}

export function verifyPasswordSHA256(password: string, hashedPassword: string): boolean {
  const inputHash = hashPasswordSHA256(password);
  return inputHash === hashedPassword;
}

export function createUserMock(username: string, password: string): boolean {
  try {
    // Check if user already exists
    const existingUser = mockUsers.find(u => u.username === username);
    if (existingUser) {
      return false;
    }

    const hashedPassword = hashPasswordSHA256(password);
    const newUser: User = {
      id: mockUsers.length + 1,
      username: username,
      password: hashedPassword,
      created_at: new Date()
    };

    mockUsers.push(newUser);
    return true;
  } catch (error) {
    console.error('Error creating user:', error);
    return false;
  }
}

export function authenticateUserMock(username: string, password: string): User | null {
  try {
    const user = mockUsers.find(u => u.username === username);
    
    if (!user) {
      return null;
    }
    
    const isValidPassword = verifyPasswordSHA256(password, user.password);
    
    if (!isValidPassword) {
      return null;
    }
    
    return user;
  } catch (error) {
    console.error('Error authenticating user:', error);
    return null;
  }
}

export function initializeMockDatabase(): void {
  try {
    // Clear existing users
    mockUsers = [];
    
    // Create default user
    createUserMock('SPB', 'Spb@123456');
    console.log('Mock database initialized with default user SPB');
  } catch (error) {
    console.error('Error initializing mock database:', error);
  }
}

export function getMockUsers(): User[] {
  return mockUsers.map(user => ({
    ...user,
    password: '***HIDDEN***'
  })) as User[];
}
