import { NextRequest, NextResponse } from 'next/server';
import { hashPassword } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    const { password } = await request.json();

    if (!password) {
      return NextResponse.json(
        { error: 'Password is required' },
        { status: 400 }
      );
    }

    const hashedPassword = await hashPassword(password);

    return NextResponse.json({
      success: true,
      originalPassword: password,
      hashedPassword: hashedPassword,
      hashLength: hashedPassword.length
    });

  } catch (error) {
    console.error('Hash test error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
