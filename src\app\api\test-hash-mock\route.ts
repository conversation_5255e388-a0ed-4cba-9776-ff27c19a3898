import { NextRequest, NextResponse } from 'next/server';
import { hashPasswordSHA256, verifyPasswordSHA256 } from '@/lib/auth-mock';

export async function POST(request: NextRequest) {
  try {
    const { password } = await request.json();

    if (!password) {
      return NextResponse.json(
        { error: 'Password is required' },
        { status: 400 }
      );
    }

    const hashedPassword = hashPasswordSHA256(password);
    const testPassword = 'Spb@123456';
    const testHash = hashPasswordSHA256(testPassword);
    const verifyResult = verifyPasswordSHA256(password, hashedPassword);
    const verifyTestResult = verifyPasswordSHA256(testPassword, testHash);

    return NextResponse.json({
      success: true,
      input: {
        password: password,
        hashedPassword: hashedPassword,
        hashLength: hashedPassword.length,
        verifyResult: verifyResult
      },
      test: {
        testPassword: testPassword,
        testHash: testHash,
        testHashLength: testHash.length,
        verifyTestResult: verifyTestResult,
        passwordsMatch: password === testPassword,
        hashesMatch: hashedPassword === testHash
      }
    });

  } catch (error) {
    console.error('Hash test error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
