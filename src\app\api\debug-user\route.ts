import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/lib/database';

export async function GET() {
  try {
    const pool = await getConnection();
    
    const result = await pool.request()
      .query('SELECT id, username, password, created_at FROM Users WHERE username = \'SPB\'');
    
    if (result.recordset.length === 0) {
      return NextResponse.json({
        success: false,
        message: 'User SPB not found'
      });
    }

    const user = result.recordset[0];
    
    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        username: user.username,
        passwordHash: user.password,
        passwordLength: user.password.length,
        created_at: user.created_at
      }
    });

  } catch (error) {
    console.error('Debug user error:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}
