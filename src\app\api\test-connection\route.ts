import { NextResponse } from 'next/server';
import { getConnection } from '@/lib/database';

export async function GET() {
  try {
    const pool = await getConnection();
    
    const result = await pool.request()
      .query('SELECT 1 as test');
    
    return NextResponse.json({
      success: true,
      message: 'Database connection successful',
      result: result.recordset[0]
    });

  } catch (error) {
    console.error('Connection test error:', error);
    return NextResponse.json(
      { 
        error: 'Database connection failed', 
        details: error.message,
        stack: error.stack 
      },
      { status: 500 }
    );
  }
}
